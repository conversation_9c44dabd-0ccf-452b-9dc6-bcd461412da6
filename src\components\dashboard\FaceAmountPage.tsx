import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { Save, RotateCcw, BarChart3, TrendingUp, User, FileText, DollarSign, Calendar, AlertTriangle } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';

const FaceAmountPage = () => {
  // Add dashboard context for policy info
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, scenarios, selectedScenarios } = useDashboard();

  // Mock customer data - in real app this would come from props or context
  const [customerData] = useState({
    name: '<PERSON>',
    customer_id: 'CUST001',
    policy_number: 'POL123456',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [selectedPolicy] = useState({
    name: 'Universal Life',
    coverage: '$500,000',
    premium: '$2,500/year'
  });

  const [faceAmountData, setFaceAmountData] = useState({
    current_death_benefit: 500000,
    want_to_change: false,
    change_immediately: false,
    change_amount: 0,
    change_by_years: false,
    change_year: ''
  });

  // State for the new modify face amount by year functionality
  const [modifyByYearData, setModifyByYearData] = useState({
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    startYear: '',
    endYear: '',
    isEditing: false,
    tableData: [] as TableRowData[]
  });

  type FaceAmountScenario = {
    id: number;
    timestamp: string;
    customer_name: string;
    policy_number: string;
    data: typeof faceAmountData;
    summary: string[];
  };

  type TableRowData = {
    age?: number;
    policyYear?: string;
    calendarYear?: number;
    faceAmount: number;
  };
  const [faceAmountHistory, setFaceAmountHistory] = useState<FaceAmountScenario[]>([]);
  const [showIllustration, setShowIllustration] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
  };

  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dobParts = selectedCustomerData.details.DOB.split('.');
    if (dobParts.length !== 3) return 40;

    const [day, month, year] = dobParts.map(Number);
    const birthDate = new Date(year, month - 1, day);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Generate table data based on selected types and year range
  const generateTableData = (): TableRowData[] => {
    const { selectedTypes, startYear, endYear } = modifyByYearData;

    if (!startYear || !endYear) return [];

    const start = parseInt(startYear);
    const end = parseInt(endYear);

    if (start > end) return [];

    const currentAge = calculateCurrentAge();
    const currentYear = new Date().getFullYear();
    const currentPolicyYear = 1; // Assuming policy started this year, could be calculated from issue date

    const data: TableRowData[] = [];

    for (let year = start; year <= end; year++) {
      const row: TableRowData = {
        faceAmount: faceAmountData.current_death_benefit // Default face amount
      };

      if (selectedTypes.age) {
        const ageAtYear = currentAge + (year - currentYear);
        row.age = ageAtYear;
      }

      if (selectedTypes.policyYear) {
        const policyYearAtYear = currentPolicyYear + (year - currentYear);
        const months = (policyYearAtYear - 1) * 12 + 1; // Convert to months
        row.policyYear = `Month ${months}`;
      }

      if (selectedTypes.calendarYear) {
        row.calendarYear = year;
      }

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  React.useEffect(() => {
    const newTableData = generateTableData();
    setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
  }, [modifyByYearData.selectedTypes, modifyByYearData.startYear, modifyByYearData.endYear, faceAmountData.current_death_benefit]);

  // Update form data
  const updateFormData = (field: keyof typeof faceAmountData, value: any) => {
    setFaceAmountData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save scenario
  const saveScenario = async () => {
    setIsSaving(true);
    try {
      const now = new Date();
      const scenarioEntry = {
        id: (Date.now().toString() + Math.random()),
        name: `Face Amount Scenario - ${(selectedCustomerData?.name || customerData.name)}`,
        policyId: selectedCustomerData?.policyNumber || customerData.policy_number,
        asIsDetails: `Current Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`,
        whatIfOptions: generateScenarioSummary(),
        category: 'face-amount' as const,
        keyPoints: generateScenarioSummary(),
        impact: 'neutral',
        data: { ...faceAmountData },
        createdAt: now,
        updatedAt: now,
      };
      await addScenario(scenarioEntry);
      showNotification('AS-IS configuration saved successfully and added to Selected Scenarios!');
    } catch (error) {
      showNotification('Error saving scenario!', 'error');
      console.error('Error saving Face Amount scenario:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Generate scenario summary
  const generateScenarioSummary = () => {
    const summary = [];

    // Current face amount
    summary.push(`Current Face Amount Death Benefit: $${faceAmountData.current_death_benefit.toLocaleString()}`);

    // Face amount changes
    if (faceAmountData.change_immediately) {
      summary.push(`Change face amount to $${faceAmountData.change_amount.toLocaleString()} now`);
    }
    if (faceAmountData.change_by_years) {
      summary.push(`Change face amount to $${faceAmountData.change_amount.toLocaleString()} starting from age ${faceAmountData.change_year}`);
    }
    if (faceAmountData.want_to_change) {
      summary.push("Modify face amount by year schedule selected");
    }

    return summary.length > 1 ? summary : ["Current face amount configuration - no changes selected"];
  };

  // Reset form
  const resetForm = () => {
    setFaceAmountData({
      current_death_benefit: 500000,
      want_to_change: false,
      change_immediately: false,
      change_amount: 0,
      change_by_years: false,
      change_year: ''
    });
    showNotification('Form reset!');
  };

  // Generate illustration data
  const generateIllustrationData = () => {
    const years = Array.from({ length: 20 }, (_, i) => 2024 + i);
    
    return years.map(year => {
      const yearsFromNow = year - 2024;
      const currentValue = faceAmountData.current_death_benefit * Math.pow(1.02, yearsFromNow);
      const newValue = faceAmountData.want_to_change && faceAmountData.change_immediately
        ? faceAmountData.change_amount * Math.pow(1.02, yearsFromNow)
        : currentValue;
      
      return {
        year,
        currentBenefit: Math.round(currentValue),
        newBenefit: Math.round(newValue)
      };
    });
  };

  // Generate illustration
  const generateIllustration = () => {
    setShowIllustration(true);
    showNotification('Face Amount illustration generated successfully!');
  };

  // Load scenario
  const loadScenario = (scenario: any) => {
    setFaceAmountData({ ...scenario.data });
    showNotification(`Scenario ${scenario.id} loaded!`);
  };



  // Filter selected scenarios for this category
  const selectedFaceAmountScenarios = scenarios.filter(
    s => selectedScenarios.includes(s.id) && s.category === 'face-amount'
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="space-y-6">
        {/* <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FACE AMOUNT ILLUSTRATION</h1>
          <p className="text-gray-600 dark:text-gray-400">Configure the face amount and death benefit options for the selected policy.</p>
        </div> */}

        {/* Notification */}
        {notification && (
          <Notification
            message={notification.message}
            type={notification.type}
            onClose={() => setNotification(null)}
          />
        )}

        {/* Show message if no policy is selected */}
        {(!selectedCustomerData || !selectedPolicyData) ? (
          <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
                <p className="text-yellow-700 dark:text-yellow-300">
                  Please go to the Policy Selection tab first to search and select a customer policy before configuring the Face Amount illustration.
                </p>
                <Button
                  onClick={() => setActiveTab('policy-selection')}
                  variant="outline"
                  className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
                >
                  Go to Policy Selection
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <>
            {/* Policy Information Section */}
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
              <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
                Policy Information
              </h3>

              <div className="grid grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Policy Number:
                  </label>
                  <input
                    type="text"
                    value={selectedCustomerData?.details?.["Policy Number"] || selectedCustomerData?.policyNumber || ''}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Customer Name:
                  </label>
                  <input
                    type="text"
                    value={selectedCustomerData?.name || ''}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Date of Birth:
                  </label>
                  <input
                    type="text"
                    value={selectedCustomerData?.details?.DOB || 'N/A'}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Issue Date:
                  </label>
                  <input
                    type="text"
                    value={(selectedPolicyData as any)?.issueDate || 'N/A'}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Policy Type:
                  </label>
                  <input
                    type="text"
                    value={selectedPolicyData?.name || ''}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-bold text-black mb-2">
                    Coverage Amount:
                  </label>
                  <input
                    type="text"
                    value={selectedPolicyData?.coverage || ''}
                    readOnly
                    className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-gray-100"
                  />
                </div>
              </div>
            </div>

            {/* Section 1: Current Face Amount Death Benefit */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-blue-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              Scenarios
            </h3>

            {/* Scenarios Description */}
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 mb-6">
              <p className="text-lg text-gray-800 leading-relaxed">
                You may want to increase or decrease your policy's face amount based on your current protection needs. Changing the death benefit will impact premiums, cash value growth, and overall policy performance. Scenarios will be based on the Current Interest Rate.
              </p>
            </div>

            {/* Current Face Amount Death Benefit Display */}
            <div className="bg-green-50 p-6 rounded-lg border border-green-200 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-xl font-bold text-black mb-2">Current Face Amount Death Benefit</h4>
                  <p className="text-gray-600">Your current policy death benefit amount</p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-green-700">
                    ${(selectedPolicyData?.coverage?.replace(/[^0-9]/g, '') || faceAmountData.current_death_benefit.toString()).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </div>
                  <p className="text-sm text-gray-500">Death Benefit</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-bold text-black mb-2">
                  Current Face Amount Death Benefit:
                </label>
                <input
                  type="number"
                  value={faceAmountData.current_death_benefit}
                  onChange={(e) => updateFormData('current_death_benefit', parseInt(e.target.value) || 0)}
                  className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                  step="10000"
                  placeholder="Enter current death benefit amount"
                />
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg border">
              <div className="space-y-4">
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_immediately}
                      onChange={(e) => updateFormData('change_immediately', e.target.checked)}
                      className="mr-2"
                    />
                    Change to New Face Amount now
                  </label>
                  {faceAmountData.change_immediately && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.change_by_years}
                      onChange={(e) => updateFormData('change_by_years', e.target.checked)}
                      className="mr-2"
                    />
                    Change starting from age
                  </label>
                  {faceAmountData.change_by_years && (
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">Starting Age:</label>
                        <select
                          value={faceAmountData.change_year}
                          onChange={(e) => updateFormData('change_year', e.target.value)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                        >
                          <option value="">Select Age</option>
                          {Array.from({ length: 50 }, (_, i) => 25 + i).map(age => (
                            <option key={age} value={age}>{age}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-bold text-black mb-1">
                          New Face Amount:
                        </label>
                        <input
                          type="number"
                          value={faceAmountData.change_amount}
                          onChange={(e) => updateFormData('change_amount', parseInt(e.target.value) || 0)}
                          className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          step="10000"
                          placeholder="Enter new face amount"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-6">
                <div>
                  <label className="flex items-center text-black font-semibold mb-2">
                    <input
                      type="checkbox"
                      checked={faceAmountData.want_to_change}
                      onChange={(e) => updateFormData('want_to_change', e.target.checked)}
                      className="mr-2"
                    />
                    Modify face amount by year
                  </label>
                  {faceAmountData.want_to_change && (
                    <div className="mt-4 space-y-4">
                      {/* Type Selection Checkboxes */}
                      <div className="grid grid-cols-3 gap-4">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.age}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, age: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.policyYear}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, policyYear: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={modifyByYearData.selectedTypes.calendarYear}
                            onChange={(e) => setModifyByYearData(prev => ({
                              ...prev,
                              selectedTypes: { ...prev.selectedTypes, calendarYear: e.target.checked }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Start and End Year Dropdowns */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">Start Year:</label>
                          <select
                            value={modifyByYearData.startYear}
                            onChange={(e) => setModifyByYearData(prev => ({ ...prev, startYear: e.target.value }))}
                            className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          >
                            <option value="">Select Start Year</option>
                            {Array.from({ length: 50 }, (_, i) => 2024 + i).map(year => (
                              <option key={year} value={year}>{year}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-bold text-black mb-1">End Year:</label>
                          <select
                            value={modifyByYearData.endYear}
                            onChange={(e) => setModifyByYearData(prev => ({ ...prev, endYear: e.target.value }))}
                            className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                          >
                            <option value="">Select End Year</option>
                            {Array.from({ length: 50 }, (_, i) => 2024 + i).map(year => (
                              <option key={year} value={year}>{year}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Modify Schedule Button */}
                      <div className="flex justify-start">
                        <button
                          onClick={() => setModifyByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                        >
                          {modifyByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                        </button>
                      </div>

                      {/* Data Table */}
                      <div className="mt-4">
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300">
                            <thead>
                              <tr className="bg-gray-100">
                                {modifyByYearData.selectedTypes.age && (
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                                )}
                                {modifyByYearData.selectedTypes.policyYear && (
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                                )}
                                {modifyByYearData.selectedTypes.calendarYear && (
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                                )}
                                <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Face Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {/* Table rows will be populated based on selected types and year range */}
                              {modifyByYearData.tableData.length === 0 ? (
                                <tr>
                                  <td
                                    colSpan={
                                      (modifyByYearData.selectedTypes.age ? 1 : 0) +
                                      (modifyByYearData.selectedTypes.policyYear ? 1 : 0) +
                                      (modifyByYearData.selectedTypes.calendarYear ? 1 : 0) + 1
                                    }
                                    className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                  >
                                    Select types and year range to populate table
                                  </td>
                                </tr>
                              ) : (
                                modifyByYearData.tableData.map((row, index) => (
                                  <tr key={index}>
                                    {modifyByYearData.selectedTypes.age && (
                                      <td className="border border-gray-300 px-4 py-2">{row.age}</td>
                                    )}
                                    {modifyByYearData.selectedTypes.policyYear && (
                                      <td className="border border-gray-300 px-4 py-2">{row.policyYear}</td>
                                    )}
                                    {modifyByYearData.selectedTypes.calendarYear && (
                                      <td className="border border-gray-300 px-4 py-2">{row.calendarYear}</td>
                                    )}
                                    <td className="border border-gray-300 px-4 py-2">
                                      <input
                                        type="number"
                                        value={row.faceAmount}
                                        readOnly={!modifyByYearData.isEditing}
                                        onChange={(e) => {
                                          if (modifyByYearData.isEditing) {
                                            const newTableData = [...modifyByYearData.tableData];
                                            newTableData[index].faceAmount = parseInt(e.target.value) || 0;
                                            setModifyByYearData(prev => ({ ...prev, tableData: newTableData }));
                                          }
                                        }}
                                        className={`w-full p-2 border rounded ${
                                          modifyByYearData.isEditing
                                            ? 'border-blue-300 bg-white'
                                            : 'border-gray-300 bg-gray-100'
                                        }`}
                                        step="10000"
                                      />
                                    </td>
                                  </tr>
                                ))
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          {/* Remove the old grid of four buttons and add new styled buttons like AsIsPage */}
          <div className="flex flex-wrap gap-4 justify-center mb-8">
            <Button
              onClick={saveScenario}
              variant="primary"
              loading={isSaving}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Face Amount Illustration</span>
            </Button>
            <Button
              onClick={() => { setFaceAmountHistory([]); showNotification('All face amount scenarios reset!'); }}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>

          {/* Illustration Results */}
          {showIllustration && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-green-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <BarChart3 className="w-6 h-6" />
                Face Amount Illustration Results
              </h3>
              
              <div className="mb-6">
                <h4 className="font-bold text-black mb-2">Current Scenario:</h4>
                <p>• Current Face Amount Death Benefit: ${faceAmountData.current_death_benefit.toLocaleString()}</p>

                {(faceAmountData.change_immediately || faceAmountData.change_by_years || faceAmountData.want_to_change) && (
                  <div className="mt-4">
                    <h4 className="font-bold text-black mb-2">Face Amount Changes:</h4>
                    {faceAmountData.change_immediately && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} now</p>
                    )}
                    {faceAmountData.change_by_years && (
                      <p>• Change to ${faceAmountData.change_amount.toLocaleString()} starting from age {faceAmountData.change_year}</p>
                    )}
                    {faceAmountData.want_to_change && (
                      <p>• Modify face amount by year schedule</p>
                    )}
                  </div>
                )}
              </div>

              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={generateIllustrationData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis tickFormatter={(value: number | string) => `$${(Number(value) / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value: number | string) => [`$${Number(value).toLocaleString()}`, '']} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="currentBenefit" 
                      stroke="#2563eb" 
                      strokeWidth={3}
                      name="Current Death Benefit"
                    />
                    {faceAmountData.want_to_change && faceAmountData.change_immediately && (
                      <Line 
                        type="monotone" 
                        dataKey="newBenefit" 
                        stroke="#dc2626" 
                        strokeWidth={3}
                        strokeDasharray="10 5"
                        name="New Death Benefit"
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* Scenario Comparison */}
          {showComparison && (
            <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-purple-500">
              <h3 className="text-2xl font-bold text-black mb-6 flex items-center gap-2">
                <TrendingUp className="w-6 h-6" />
                Face Amount Scenario Comparison
              </h3>
              
              {faceAmountHistory.length > 0 ? (
                <div className="space-y-4">
                  <h4 className="font-bold text-black">Saved Scenarios:</h4>
                  {faceAmountHistory.map((scenario) => (
                    <div key={scenario.id} className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h5 className="font-bold text-black">Scenario {scenario.id} - {scenario.timestamp}</h5>
                          <p className="text-sm text-gray-600">Customer: {scenario.customer_name}</p>
                          <p className="text-sm text-gray-600">Policy: {scenario.policy_number}</p>
                        </div>
                        <button
                          onClick={() => loadScenario(scenario)}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors text-sm"
                        >
                          Load Scenario
                        </button>
                      </div>
                      <div>
                        <p className="font-semibold text-black mb-1">Summary:</p>
                        <ul className="text-sm text-gray-700">
                          {scenario.summary.map((item, index) => (
                            <li key={index}>• {item}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <p className="text-gray-600">No saved scenarios available. Save a scenario first to enable comparison.</p>
                </div>
              )}
            </div>
          )}

          {/* Selected Scenarios Section */}
          <div className="bg-white p-8 rounded-xl shadow-lg mb-8 border-l-6 border-indigo-500">
            <h3 className="text-2xl font-bold text-black mb-6 pb-4 border-b-2 border-gray-200">
              Selected Scenarios
            </h3>

            {scenarios.filter(s => selectedScenarios.includes(s.id) && s.category === 'face-amount').length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-yellow-600" />
                </div>
                <h4 className="text-xl font-semibold text-gray-700 mb-2">No Scenarios Saved</h4>
                <p className="text-gray-600 mb-4">
                  No face amount scenarios have been saved yet. Save a scenario above to see it here.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {scenarios
                  .filter(s => selectedScenarios.includes(s.id) && s.category === 'face-amount')
                  .map((scenario) => (
                    <div key={scenario.id} className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h4 className="text-lg font-bold text-black">{scenario.name}</h4>
                          <p className="text-sm text-gray-600">
                            Created: {new Date(scenario.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium">
                          Face Amount
                        </span>
                      </div>

                      <div className="mb-4">
                        <h5 className="font-semibold text-black mb-2">AS-IS Details:</h5>
                        <p className="text-gray-700">{scenario.asIsDetails}</p>
                      </div>

                      {scenario.whatIfOptions && scenario.whatIfOptions.length > 0 && (
                        <div>
                          <h5 className="font-semibold text-black mb-2">What-If Options:</h5>
                          <ul className="text-gray-700 space-y-1">
                            {scenario.whatIfOptions.map((option, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-indigo-500 mr-2">•</span>
                                <span>{option}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            )}
          </div>
          </>
        )}
      </div>
    </div>
  );
};

export default FaceAmountPage;